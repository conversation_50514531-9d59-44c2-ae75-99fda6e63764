<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Chat Widget Fullscreen Test</title>
  <script async src="./chat-widget.js"></script>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      color: white;
    }
    .content {
      max-width: 800px;
      margin: 0 auto;
      padding: 40px 20px;
    }
    h1 {
      text-align: center;
      margin-bottom: 30px;
      font-size: 2.5em;
    }
    .feature-list {
      background: rgba(255, 255, 255, 0.1);
      padding: 30px;
      border-radius: 10px;
      margin: 20px 0;
      backdrop-filter: blur(10px);
    }
    .feature-list h2 {
      margin-top: 0;
      color: #fff;
    }
    .feature-list ul {
      list-style-type: none;
      padding: 0;
    }
    .feature-list li {
      padding: 10px 0;
      border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    .feature-list li:last-child {
      border-bottom: none;
    }
    .feature-list li::before {
      content: "✓ ";
      color: #4ade80;
      font-weight: bold;
      margin-right: 10px;
    }
    .instructions {
      background: rgba(255, 255, 255, 0.1);
      padding: 20px;
      border-radius: 10px;
      margin: 20px 0;
      backdrop-filter: blur(10px);
    }
    .instructions h3 {
      margin-top: 0;
      color: #fbbf24;
    }
    .instructions p {
      margin: 10px 0;
    }
    .instructions code {
      background: rgba(0, 0, 0, 0.3);
      padding: 2px 6px;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
    }
  </style>
</head>
<body>
  <div class="content">
    <h1>🚀 Chat Widget with Fullscreen Mode</h1>
    
    <div class="feature-list">
      <h2>✨ New Fullscreen Features</h2>
      <ul>
        <li>Click the expand icon in the chat header to enter fullscreen mode</li>
        <li>Click the compress icon to exit fullscreen mode</li>
        <li>Press <code>F11</code> to toggle fullscreen mode</li>
        <li>Press <code>Escape</code> to exit fullscreen mode</li>
        <li>Smooth transitions with CSS animations</li>
        <li>Maintains chat input focus during transitions</li>
        <li>Auto-scrolls to latest messages</li>
        <li>Works on both desktop and mobile devices</li>
      </ul>
    </div>

    <div class="instructions">
      <h3>🎯 How to Test</h3>
      <p>1. Click the chat bubble in the bottom-right corner to open the chat widget</p>
      <p>2. Look for the expand/fullscreen icon next to the close button in the chat header</p>
      <p>3. Click the fullscreen icon to expand the chat to full screen</p>
      <p>4. Try typing some messages to see how it works in fullscreen mode</p>
      <p>5. Use keyboard shortcuts: <code>F11</code> to toggle, <code>Escape</code> to exit</p>
      <p>6. Click the compress icon or use shortcuts to return to normal size</p>
    </div>

    <div class="instructions">
      <h3>🔧 Technical Implementation</h3>
      <p>The fullscreen mode uses CSS classes and transitions to smoothly transform the chat widget:</p>
      <p>• <code>.fullscreen</code> class applies fixed positioning covering the entire viewport</p>
      <p>• CSS transitions provide smooth 300ms animations</p>
      <p>• JavaScript handles icon switching and keyboard shortcuts</p>
      <p>• Focus management ensures good user experience</p>
    </div>
  </div>
</body>
</html>
